<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire API 测试工具 - 精简版</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1400px; margin: 0 auto; background: white;
            border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 20px; text-align: center; border-radius: 12px 12px 0 0;
        }
        .header h1 { margin: 0; font-size: 2em; font-weight: 300; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .main-content { padding: 20px; }
        
        /* 核心组件样式 */
        .section {
            background: #f8f9fa; margin-bottom: 20px; border-radius: 8px;
            border: 1px solid #e9ecef; overflow: hidden;
        }
        .section-header {
            background: #007bff; color: white; padding: 12px 20px;
            font-weight: 600; font-size: 1.1em;
        }
        .section-body { padding: 20px; }
        
        /* 表单样式 */
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; }
        .form-group input, .form-group select { 
            width: 100%; padding: 10px; border: 1px solid #ddd; 
            border-radius: 4px; font-size: 14px;
        }
        .form-row { display: flex; gap: 15px; flex-wrap: wrap; }
        .form-row .form-group { flex: 1; min-width: 200px; }
        
        /* 按钮样式 */
        .btn {
            padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;
            font-size: 14px; font-weight: 500; transition: all 0.3s ease; margin: 4px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        
        /* 账号选择器 */
        .account-selector {
            background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
            border: 2px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 20px;
        }
        .account-selector h4 { margin: 0 0 10px 0; color: #0056b3; }
        .quick-buttons { display: flex; gap: 8px; flex-wrap: wrap; margin-top: 10px; }
        .quick-buttons .btn { flex: 1; min-width: 120px; }
        
        /* 后台用户选择器 */
        .backend-user-selector {
            background: linear-gradient(135deg, #f0f8ff 0%, #e8f5e8 100%);
            border: 2px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 20px;
            display: none;
        }
        .backend-user-selector h4 { margin: 0 0 10px 0; color: #0056b3; }
        .user-info { 
            background: white; border: 1px solid #dee2e6; border-radius: 4px; 
            padding: 10px; margin-bottom: 10px; font-size: 0.9em;
        }
        
        /* 测试配置面板 */
        .test-config-panel {
            background: linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%);
            border: 2px solid #ffc107; border-radius: 8px; padding: 15px; margin-bottom: 20px;
        }
        .test-config-panel h4 { margin: 0 0 15px 0; color: #856404; }
        .config-row { display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 15px; }
        .config-row .form-group { flex: 1; min-width: 150px; }
        
        /* 测试卡片 */
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .test-card {
            background: white; border: 1px solid #e9ecef; border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;
        }
        .test-card-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e9ecef; }
        .test-title { margin: 0; color: #495057; font-size: 1.1em; }
        .test-type { 
            display: inline-block; background: #007bff; color: white; 
            padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-top: 5px;
        }
        .test-card-body { padding: 15px; }
        
        /* 测试结果 */
        .test-result { margin-top: 15px; padding: 10px; border-radius: 4px; border-left: 4px solid #6c757d; }
        .result-success { background: #d4edda; border-left-color: #28a745; }
        .result-error { background: #f8d7da; border-left-color: #dc3545; }
        .result-pending { background: #fff3cd; border-left-color: #ffc107; }
        
        /* 状态指示器 */
        .auth-status {
            display: inline-block; padding: 4px 8px; border-radius: 4px;
            font-size: 0.9em; font-weight: 500;
        }
        .auth-success { background: #d4edda; color: #155724; }
        .auth-pending { background: #fff3cd; color: #856404; }
        .auth-failed { background: #f8d7da; color: #721c24; }
        
        /* 统计信息 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 15px; border-radius: 8px; text-align: center;
        }
        .stat-number { font-size: 1.8em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.8em; opacity: 0.9; }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { margin: 10px; }
            .main-content { padding: 15px; }
            .form-row, .config-row { flex-direction: column; }
            .test-grid { grid-template-columns: 1fr; }
            .quick-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 GoMyHire API 测试工具</h1>
            <p>精简版 - 智能动态测试用例生成</p>
        </div>
        
        <div class="main-content">
            <!-- 邮箱账号选择器 -->
            <div class="account-selector">
                <h4>📧 邮箱账号选择</h4>
                <select id="accountSelector" onchange="switchAccount()">
                    <option value="">选择邮箱账号...</option>
                </select>
                <div class="quick-buttons">
                    <button type="button" class="btn btn-primary" onclick="switchToAccount('general')">
                        📧 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-success" onclick="switchToAccount('jcy')">
                        👤 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-info" onclick="switchToAccount('skymirror')">
                        🌟 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-warning" onclick="testAllAccounts()">
                        🧪 测试所有邮箱
                    </button>
                </div>
                <div class="user-info">
                    <span id="authStatus" class="auth-status auth-pending">正在初始化...</span>
                    <div id="currentAccountInfo" style="margin-top: 5px; font-size: 0.9em;">未选择账号</div>
                </div>
            </div>

            <!-- 后台用户选择器 -->
            <div id="backendUserSelector" class="backend-user-selector">
                <h4>👤 后台用户选择</h4>
                <div id="backendUserInfo" class="user-info">正在加载后台用户列表...</div>
                <select id="backendUserSelect" onchange="selectBackendUser()">
                    <option value="">选择后台用户...</option>
                </select>
                <div id="selectedBackendUserInfo" class="user-info">未选择后台用户</div>
            </div>

            <!-- 测试配置面板 -->
            <div class="test-config-panel">
                <h4>⚙️ 测试配置</h4>
                <div class="config-row">
                    <div class="form-group">
                        <label for="testCount">测试条数</label>
                        <select id="testCount">
                            <option value="5">5条测试</option>
                            <option value="10" selected>10条测试</option>
                            <option value="15">15条测试</option>
                            <option value="20">20条测试</option>
                            <option value="30">30条测试</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="orderType">订单类型</label>
                        <select id="orderType">
                            <option value="mixed" selected>混合类型</option>
                            <option value="pickup">仅接机</option>
                            <option value="dropoff">仅送机</option>
                            <option value="charter">仅包车</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="regionType">地区选择</label>
                        <select id="regionType">
                            <option value="mixed" selected>混合地区</option>
                            <option value="kl">吉隆坡</option>
                            <option value="penang">槟城</option>
                            <option value="johor">柔佛</option>
                            <option value="sabah">沙巴</option>
                        </select>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-success" onclick="generateAndRunTests()">🚀 生成并运行测试</button>
                    <button class="btn btn-warning" onclick="clearAllResults()">🧹 清除结果</button>
                    <button class="btn btn-primary" onclick="previewTestCases()">👁️ 预览测试用例</button>
                </div>
            </div>

            <!-- 手动输入订单测试模块 -->
            <div class="section">
                <div class="section-header">
                    ✏️ 手动输入订单测试
                </div>
                <div class="section-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualOrderType">订单类型</label>
                            <select id="manualOrderType" onchange="updateManualSubCategory()">
                                <option value="pickup">接机服务</option>
                                <option value="dropoff">送机服务</option>
                                <option value="charter">包车服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manualCarType">车型选择</label>
                            <select id="manualCarType">
                                <option value="1">Comfort 5 Seater</option>
                                <option value="5">Economy 5 Seater</option>
                                <option value="15">Economy 7 Seater</option>
                                <option value="20">Comfort 7 Seater</option>
                                <option value="25">Mini Bus 14 Seater</option>
                                <option value="26">Bus 20+ Seater</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manualRegion">地区选择</label>
                            <select id="manualRegion">
                                <option value="1">吉隆坡/雪兰莪</option>
                                <option value="2">槟城</option>
                                <option value="3">柔佛</option>
                                <option value="4">沙巴</option>
                                <option value="12">马六甲</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualCustomerName">客户姓名</label>
                            <input type="text" id="manualCustomerName" placeholder="请输入客户姓名" value="测试客户">
                        </div>
                        <div class="form-group">
                            <label for="manualCustomerContact">客户电话</label>
                            <input type="text" id="manualCustomerContact" placeholder="+***********" value="+***********">
                        </div>
                        <div class="form-group">
                            <label for="manualCustomerEmail">客户邮箱</label>
                            <input type="email" id="manualCustomerEmail" placeholder="<EMAIL>" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualPickup">接机地址</label>
                            <input type="text" id="manualPickup" placeholder="请输入接机地址" value="Kuala Lumpur International Airport (KLIA)">
                        </div>
                        <div class="form-group">
                            <label for="manualDestination">目的地址</label>
                            <input type="text" id="manualDestination" placeholder="请输入目的地址" value="KLCC - Kuala Lumpur City Centre">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualDate">服务日期</label>
                            <input type="date" id="manualDate">
                        </div>
                        <div class="form-group">
                            <label for="manualTime">服务时间</label>
                            <input type="time" id="manualTime" value="10:00">
                        </div>
                        <div class="form-group">
                            <label for="manualPassengers">乘客人数</label>
                            <input type="number" id="manualPassengers" min="1" max="50" value="2">
                        </div>
                        <div class="form-group">
                            <label for="manualLuggage">行李件数</label>
                            <input type="number" id="manualLuggage" min="0" max="100" value="2">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="manualReference">OTA参考号</label>
                        <input type="text" id="manualReference" placeholder="自动生成或手动输入" value="">
                    </div>

                    <div class="form-group">
                        <label for="manualRequirement">特殊要求</label>
                        <textarea id="manualRequirement" rows="3" placeholder="请输入特殊要求（可选）">TESTING - 手动输入测试订单，请勿处理</textarea>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button type="button" class="btn btn-info" onclick="previewManualOrder()">👁️ 预览订单数据</button>
                        <button type="button" class="btn btn-success" onclick="submitManualOrder()">🚀 提交测试订单</button>
                        <button type="button" class="btn btn-warning" onclick="resetManualForm()">🔄 重置表单</button>
                    </div>

                    <!-- 手动订单测试结果 -->
                    <div id="manualOrderResult" class="test-result" style="display: none; margin-top: 20px;"></div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successTests">0</div>
                    <div class="stat-label">成功数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">失败数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>

            <!-- 测试用例网格 -->
            <div id="orderTestGrid" class="test-grid">
                <!-- 动态生成测试卡片 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE_URL = 'https://gomyhire.com.my/api';
        let authToken = null;
        let currentAccount = null;
        let availableBackendUsers = [];
        let selectedBackendUser = null;
        let availableCarTypes = []; // 存储从API获取的车型列表
        let availableSubCategories = []; // 存储从API获取的子分类列表
        let availableRegions = []; // 存储从API获取的地区列表
        let availableLanguages = []; // 存储从API获取的语言列表
        let orderTestStats = { total: 0, success: 0, failed: 0 };
        let generatedTestCases = [];

        // 邮箱账号配置
        const realLoginAccounts = [
            {
                id: 'general',
                email: '<EMAIL>',
                password: 'Gomyhire@123456',
                isDefault: true
            },
            {
                id: 'jcy',
                email: '<EMAIL>',
                password: 'Yap123',
                isDefault: false
            },
            {
                id: 'skymirror',
                email: '<EMAIL>',
                password: 'Sky@114788',
                isDefault: false
            }
        ];

        // 地址模板库 - 马来西亚真实地址
        const addressTemplates = [
            {
                id: 'airport_kl',
                name: '机场 ↔ 吉隆坡市中心',
                pickup: 'Kuala Lumpur International Airport (KLIA)',
                destination: 'KLCC - Kuala Lumpur City Centre',
                category: 'airport',
                region: 'kl'
            },
            {
                id: 'airport_klia2',
                name: 'KLIA2 ↔ 双子塔',
                pickup: 'KLIA2 Terminal',
                destination: 'Petronas Twin Towers KLCC',
                category: 'airport',
                region: 'kl'
            },
            {
                id: 'hotel_airport',
                name: '酒店 ↔ 机场',
                pickup: 'Hotel Sentral Kuala Lumpur',
                destination: 'KLIA Terminal 1',
                category: 'airport',
                region: 'kl'
            },
            {
                id: 'city_tour',
                name: '吉隆坡市区游',
                pickup: 'Merdeka Square (Independence Square)',
                destination: 'Batu Caves Temple',
                category: 'tour',
                region: 'kl'
            },
            {
                id: 'genting_trip',
                name: '云顶高原一日游',
                pickup: 'Kuala Lumpur City Center',
                destination: 'Genting Highlands Resort',
                category: 'tour',
                region: 'kl'
            },
            {
                id: 'penang_tour',
                name: '槟城历史游',
                pickup: 'Penang International Airport',
                destination: 'Georgetown Heritage Area',
                category: 'tour',
                region: 'penang'
            },
            {
                id: 'johor_sg',
                name: '柔佛新加坡跨境',
                pickup: 'Johor Bahru CIQ',
                destination: 'Changi Airport Singapore',
                category: 'airport',
                region: 'johor'
            },
            {
                id: 'sabah_tour',
                name: '沙巴当地游',
                pickup: 'Kota Kinabalu Airport',
                destination: 'Kota Kinabalu City Tour',
                category: 'tour',
                region: 'sabah'
            }
        ];

        // 客户信息模板
        const customerTemplates = {
            chinese: [
                { name: '张三', email: '<EMAIL>' },
                { name: '李四', email: '<EMAIL>' },
                { name: '王五', email: '<EMAIL>' },
                { name: '赵六', email: '<EMAIL>' },
                { name: '陈七', email: '<EMAIL>' }
            ],
            english: [
                { name: 'John Smith', email: '<EMAIL>' },
                { name: 'Mary Johnson', email: '<EMAIL>' },
                { name: 'David Brown', email: '<EMAIL>' },
                { name: 'Sarah Wilson', email: '<EMAIL>' },
                { name: 'Michael Davis', email: '<EMAIL>' }
            ],
            malay: [
                { name: 'Ahmad Abdullah', email: '<EMAIL>' },
                { name: 'Siti Nurhaliza', email: '<EMAIL>' },
                { name: 'Muhammad Ali', email: '<EMAIL>' },
                { name: 'Fatimah Zahra', email: '<EMAIL>' },
                { name: 'Hassan Ibrahim', email: '<EMAIL>' }
            ]
        };

        // 车型配置
        const carTypeConfig = {
            economy: [5, 15, 38], // 经济型
            comfort: [1, 20, 33], // 舒适型
            luxury: [32, 33, 36], // 豪华型
            minibus: [25, 26], // 小巴
            bus: [26] // 大巴
        };

        // 地区配置
        const regionConfig = {
            kl: { id: 1, name: '吉隆坡/雪兰莪' },
            penang: { id: 2, name: '槟城' },
            johor: { id: 3, name: '柔佛' },
            sabah: { id: 4, name: '沙巴' },
            melaka: { id: 12, name: '马六甲' }
        };

        // 工具函数
        function getRandomElement(array) {
            return array[Math.floor(Math.random() * array.length)];
        }

        function getRandomDate() {
            const start = new Date(2025, 4, 1); // 2025年5月1日
            const end = new Date(2025, 4, 31); // 2025年5月31日
            const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
            return date.toISOString().split('T')[0];
        }

        function getRandomTime() {
            const hours = Math.floor(Math.random() * 24);
            const minutes = Math.floor(Math.random() * 4) * 15; // 15分钟间隔
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        function getRandomPhone() {
            const prefixes = ['+60123', '+60198', '+60176', '+60187', '+60165'];
            const prefix = getRandomElement(prefixes);
            const number = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
            return prefix + number;
        }

        function getRandomPassengerCount() {
            const weights = [
                { count: 1, weight: 15 },
                { count: 2, weight: 25 },
                { count: 3, weight: 20 },
                { count: 4, weight: 15 },
                { count: 5, weight: 10 },
                { count: 6, weight: 8 },
                { count: 8, weight: 4 },
                { count: 10, weight: 2 },
                { count: 15, weight: 1 }
            ];

            const totalWeight = weights.reduce((sum, item) => sum + item.weight, 0);
            let random = Math.random() * totalWeight;

            for (const item of weights) {
                random -= item.weight;
                if (random <= 0) return item.count;
            }
            return 2; // 默认值
        }

        /**
         * @function getCarTypeForPassengers - 根据乘客人数智能选择车型
         * @param {number} passengerCount - 乘客人数
         * @returns {number} 车型ID
         * @description 根据乘客人数从可用车型中智能选择合适的车型
         */
        function getCarTypeForPassengers(passengerCount) {
            // 如果没有可用车型，使用备用逻辑
            if (availableCarTypes.length === 0) {
                if (passengerCount <= 4) return getRandomElement(carTypeConfig.economy);
                if (passengerCount <= 6) return getRandomElement(carTypeConfig.comfort);
                if (passengerCount <= 8) return getRandomElement(carTypeConfig.luxury);
                if (passengerCount <= 25) return getRandomElement(carTypeConfig.minibus);
                return getRandomElement(carTypeConfig.bus);
            }

            // 使用动态车型数据
            const suitableCarTypes = availableCarTypes.filter(carType => {
                const seatNumber = carType.seat_number || 5; // 默认5座
                return seatNumber >= passengerCount;
            });

            if (suitableCarTypes.length === 0) {
                // 如果没有合适的车型，选择最大的
                const maxSeatCarType = availableCarTypes.reduce((max, carType) => {
                    const maxSeats = max.seat_number || 0;
                    const currentSeats = carType.seat_number || 0;
                    return currentSeats > maxSeats ? carType : max;
                });
                return maxSeatCarType.id;
            }

            // 选择最小但足够的车型（更经济）
            const optimalCarType = suitableCarTypes.reduce((optimal, carType) => {
                const optimalSeats = optimal.seat_number || 999;
                const currentSeats = carType.seat_number || 999;
                return currentSeats < optimalSeats ? carType : optimal;
            });

            return optimalCarType.id;
        }

        // 动态测试用例生成器
        function generateTestCase(index, orderType, regionType) {
            const orderTypes = ['pickup', 'dropoff', 'charter'];
            const actualOrderType = orderType === 'mixed' ? getRandomElement(orderTypes) : orderType;

            // 选择地址模板
            let availableTemplates = addressTemplates;
            if (regionType !== 'mixed') {
                availableTemplates = addressTemplates.filter(t => t.region === regionType);
            }
            const template = getRandomElement(availableTemplates);

            // 选择客户信息
            const customerType = getRandomElement(['chinese', 'english', 'malay']);
            const customer = getRandomElement(customerTemplates[customerType]);

            // 生成订单数据
            const passengerCount = getRandomPassengerCount();
            const carTypeId = getCarTypeForPassengers(passengerCount);

            // 使用动态地区数据
            let regionId = regionConfig[template.region]?.id || 1;
            if (availableRegions.length > 0) {
                const region = availableRegions.find(r => r.name?.includes(regionConfig[template.region]?.name)) ||
                              availableRegions[0];
                regionId = region.id;
            }

            // 使用动态子分类数据
            let subCategoryId = getSubCategoryByType(actualOrderType);
            if (!subCategoryId) {
                // 回退到硬编码值
                subCategoryId = actualOrderType === 'pickup' ? 2 : actualOrderType === 'dropoff' ? 3 : 4;
            }

            // 使用动态语言数据
            const languageIds = getLanguagesByCustomerType(customerType);

            // 确定接送地址
            let pickup, destination;
            if (actualOrderType === 'pickup') {
                pickup = template.pickup;
                destination = template.destination;
            } else if (actualOrderType === 'dropoff') {
                pickup = template.destination;
                destination = template.pickup;
            } else {
                pickup = template.pickup;
                destination = template.destination;
            }

            const testCase = {
                name: `${actualOrderType === 'pickup' ? '接机' : actualOrderType === 'dropoff' ? '送机' : '包车'}服务 - ${template.name}`,
                type: actualOrderType,
                description: `${template.name} - ${passengerCount}人${customerType === 'chinese' ? '中文' : customerType === 'english' ? '英文' : '马来'}客户`,
                data: {
                    sub_category_id: subCategoryId,
                    car_type_id: carTypeId,
                    incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                    ota_reference_number: `${actualOrderType.toUpperCase()}_${Date.now()}_${index}`,
                    customer_name: customer.name,
                    customer_contact: getRandomPhone(),
                    customer_email: customer.email,
                    pickup: pickup,
                    destination: destination,
                    date: getRandomDate(),
                    time: getRandomTime(),
                    passenger_number: passengerCount,
                    luggage_number: Math.min(passengerCount + Math.floor(Math.random() * 3), passengerCount * 2),
                    driving_region_id: regionId,
                    languages_id_array: languageIds,
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 动态生成测试用例'
                }
            };

            return testCase;
        }

        // 生成并运行测试
        async function generateAndRunTests() {
            const testCount = parseInt(document.getElementById('testCount').value);
            const orderType = document.getElementById('orderType').value;
            const regionType = document.getElementById('regionType').value;

            // 验证认证状态
            if (!authToken) {
                alert('请先选择邮箱账号进行认证');
                return;
            }

            // 生成测试用例
            generatedTestCases = [];
            for (let i = 0; i < testCount; i++) {
                generatedTestCases.push(generateTestCase(i + 1, orderType, regionType));
            }

            // 渲染测试卡片
            renderTestCards();

            // 重置统计
            orderTestStats = { total: 0, success: 0, failed: 0 };
            updateStats();

            // 运行测试
            for (let i = 0; i < generatedTestCases.length; i++) {
                await runSingleTest(i);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 1秒间隔
            }

            console.log('所有测试完成', orderTestStats);
        }

        // 预览测试用例
        function previewTestCases() {
            const testCount = parseInt(document.getElementById('testCount').value);
            const orderType = document.getElementById('orderType').value;
            const regionType = document.getElementById('regionType').value;

            // 生成测试用例
            generatedTestCases = [];
            for (let i = 0; i < testCount; i++) {
                generatedTestCases.push(generateTestCase(i + 1, orderType, regionType));
            }

            // 渲染测试卡片
            renderTestCards();

            alert(`已生成 ${testCount} 个测试用例，请查看下方卡片。点击"生成并运行测试"开始执行。`);
        }

        // 渲染测试卡片
        function renderTestCards() {
            const container = document.getElementById('orderTestGrid');
            container.innerHTML = '';

            generatedTestCases.forEach((testCase, index) => {
                const card = document.createElement('div');
                card.className = 'test-card';

                const typeColor = testCase.type === 'pickup' ? '#28a745' :
                                testCase.type === 'dropoff' ? '#007bff' : '#ffc107';

                card.innerHTML = `
                    <div class="test-card-header">
                        <h4 class="test-title">${testCase.name}</h4>
                        <span class="test-type" style="background: ${typeColor};">${testCase.type}</span>
                    </div>
                    <div class="test-card-body">
                        <p style="margin: 5px 0; color: #6c757d; font-size: 0.9em;">${testCase.description}</p>
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin: 8px 0; font-size: 0.85em;">
                            <div style="color: #28a745;">📍 接机: ${testCase.data.pickup}</div>
                            <div style="color: #dc3545;">🎯 送达: ${testCase.data.destination}</div>
                            <div style="color: #007bff;">👥 乘客: ${testCase.data.passenger_number}人 | 🧳 行李: ${testCase.data.luggage_number}件</div>
                            <div style="color: #6c757d;">📅 ${testCase.data.date} ${testCase.data.time}</div>
                        </div>
                        <button class="btn btn-primary" onclick="runSingleTest(${index})">
                            测试此订单
                        </button>
                        <div id="testResult${index}" class="test-result" style="display: none;"></div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 运行单个测试
        async function runSingleTest(index) {
            if (index < 0 || index >= generatedTestCases.length) {
                console.error('无效的测试索引:', index);
                return;
            }

            const testCase = generatedTestCases[index];
            const resultContainer = document.getElementById(`testResult${index}`);

            if (!resultContainer) {
                console.error('找不到结果容器:', `testResult${index}`);
                return;
            }

            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-pending';
            resultContainer.innerHTML = '🔄 测试进行中...';

            try {
                // 验证认证状态
                if (!authToken) {
                    throw new Error('未找到认证token，请先登录邮箱账号');
                }

                // 准备订单数据
                const orderData = prepareOrderData(testCase.data);
                const startTime = Date.now();

                // 发送API请求
                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(orderData)
                });

                const responseTime = Date.now() - startTime;
                const responseText = await response.text();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`无效的JSON响应: ${parseError.message}`);
                }

                if (result.status === true || result.status === 'true') {
                    orderTestStats.success++;
                    orderTestStats.total++;

                    resultContainer.className = 'test-result result-success';
                    resultContainer.innerHTML = `
                        ✅ <strong>测试成功</strong><br>
                        订单ID: ${result.order_id || result.data?.order_id || 'N/A'}<br>
                        消息: ${result.message || '订单创建成功'}<br>
                        <small>响应时间: ${responseTime}ms</small>
                    `;
                } else {
                    throw new Error(result.message || result.error || '订单创建失败');
                }

            } catch (error) {
                orderTestStats.failed++;
                orderTestStats.total++;

                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>测试失败</strong><br>
                    错误: ${error.message}<br>
                    <small>测试用例: ${testCase.name}</small>
                `;

                console.error(`订单测试失败: ${testCase.name}`, error);
            }

            updateStats();
        }

        /**
         * @function prepareOrderData - 准备订单数据，智能匹配后台用户
         * @param {Object} originalData - 原始订单数据
         * @returns {Object} 处理后的订单数据
         * @description 根据当前登录邮箱智能选择后台用户ID，确保订单分配给正确的负责人
         */
        function prepareOrderData(originalData) {
            const orderData = JSON.parse(JSON.stringify(originalData));

            // 优先使用智能匹配的后台用户ID
            const smartUserId = getSmartBackendUserId();
            if (smartUserId) {
                // 验证智能匹配的用户是否在可用列表中
                const smartUser = availableBackendUsers.find(u => u.id === smartUserId);
                if (smartUser) {
                    orderData.incharge_by_backend_user_id = smartUserId;
                    console.log(`🎯 智能匹配后台用户: ${smartUser.name} (ID: ${smartUserId})`);
                } else {
                    // 智能匹配失败，使用当前选中的用户
                    orderData.incharge_by_backend_user_id = selectedBackendUser?.id || 1;
                    console.log(`⚠️ 智能匹配失败，使用当前选中用户: ID ${orderData.incharge_by_backend_user_id}`);
                }
            } else {
                // 使用当前选中的后台用户或默认值
                orderData.incharge_by_backend_user_id = selectedBackendUser?.id || 1;
                console.log(`📋 使用当前选中后台用户: ID ${orderData.incharge_by_backend_user_id}`);
            }

            return orderData;
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = orderTestStats.total;
            document.getElementById('successTests').textContent = orderTestStats.success;
            document.getElementById('failedTests').textContent = orderTestStats.failed;

            const successRate = orderTestStats.total > 0 ?
                Math.round((orderTestStats.success / orderTestStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 清除所有测试结果
        function clearAllResults() {
            orderTestStats = { total: 0, success: 0, failed: 0 };
            generatedTestCases = [];
            document.getElementById('orderTestGrid').innerHTML = '';
            updateStats();
        }

        // 切换到指定账号
        async function switchToAccount(accountId) {
            const account = realLoginAccounts.find(acc => acc.id === accountId);
            if (!account) {
                console.error('账号不存在:', accountId);
                return;
            }

            // 更新选择器
            const selector = document.getElementById('accountSelector');
            if (selector) selector.value = accountId;

            // 清除之前的数据
            authToken = null;
            currentAccount = null;
            availableBackendUsers = [];
            selectedBackendUser = null;

            // 隐藏后台用户选择器
            const backendUserSelector = document.getElementById('backendUserSelector');
            if (backendUserSelector) backendUserSelector.style.display = 'none';

            // 开始认证
            updateAuthStatus(false, `正在切换到 ${account.email}...`);
            updateCurrentAccountInfo('切换中...');

            const success = await authenticateAccount(account);
            if (success) {
                currentAccount = account;
                updateCurrentAccountInfo(`当前邮箱: ${account.email}`);
                // 并行加载所有必要数据
                await Promise.all([
                    loadBackendUsers(),
                    loadCarTypes(),
                    loadSubCategories(),
                    loadRegions(),
                    loadLanguages()
                ]);
            } else {
                updateCurrentAccountInfo('认证失败');
            }
        }

        // 从选择器切换账号
        async function switchAccount() {
            const selector = document.getElementById('accountSelector');
            if (!selector || !selector.value) return;
            await switchToAccount(selector.value);
        }

        // 认证指定账号
        async function authenticateAccount(account) {
            try {
                const loginResponse = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        'email': account.email,
                        'password': account.password
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    if (loginData.status && loginData.token) {
                        const fullToken = loginData.token;
                        authToken = fullToken.includes('|') ? fullToken.split('|')[1] : fullToken;
                        updateAuthStatus(true, `✅ ${account.email} 认证成功`);
                        return true;
                    }
                }

                updateAuthStatus(false, `❌ ${account.email} 认证失败`);
                return false;

            } catch (error) {
                updateAuthStatus(false, `❌ ${account.email} 认证失败：网络错误`);
                return false;
            }
        }

        // 智能后台用户匹配规则
        const backendUserMapping = {
            '<EMAIL>': 37,  // smw
            '<EMAIL>': 310,         // Jcy
            '<EMAIL>': null     // 使用默认逻辑
        };

        /**
         * @function getSmartBackendUserId - 根据当前登录邮箱智能匹配后台用户ID
         * @returns {number|null} 匹配的后台用户ID，null表示使用默认逻辑
         */
        function getSmartBackendUserId() {
            if (!currentAccount || !currentAccount.email) return null;
            return backendUserMapping[currentAccount.email] || null;
        }

        /**
         * @function smartSelectBackendUser - 智能选择后台用户
         * @description 根据当前登录邮箱自动选择对应的后台用户，如果匹配失败则使用默认逻辑
         */
        function smartSelectBackendUser() {
            const smartUserId = getSmartBackendUserId();

            if (smartUserId) {
                // 尝试智能匹配
                const matchedUser = availableBackendUsers.find(u => u.id === smartUserId);
                if (matchedUser) {
                    selectBackendUser(smartUserId);
                    updateBackendUserInfo(`🎯 智能匹配: ${matchedUser.name} (ID: ${smartUserId})`);
                    return true;
                } else {
                    updateBackendUserInfo(`⚠️ 智能匹配失败: 未找到用户ID ${smartUserId}，使用默认选择`);
                }
            }

            // 回退到默认逻辑
            if (availableBackendUsers.length > 0) {
                selectBackendUser(availableBackendUsers[0].id);
                updateBackendUserInfo(`📋 默认选择: ${availableBackendUsers[0].name} (ID: ${availableBackendUsers[0].id})`);
                return true;
            }

            return false;
        }

        // ========== 车型管理功能 ==========

        /**
         * @function loadCarTypes - 从API加载可用车型列表
         * @returns {Array} 车型列表
         * @description 获取GoMyHire API支持的所有车型，用于动态更新选择器
         */
        async function loadCarTypes() {
            if (!authToken) return [];

            try {
                const response = await fetch(`${API_BASE_URL}/car_types`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && Array.isArray(data.data)) {
                        availableCarTypes = data.data;
                        updateCarTypeSelectors();
                        console.log(`✅ 成功加载 ${availableCarTypes.length} 个车型`);
                        return availableCarTypes;
                    } else if (data && Array.isArray(data)) {
                        // 有些API直接返回数组
                        availableCarTypes = data;
                        updateCarTypeSelectors();
                        console.log(`✅ 成功加载 ${availableCarTypes.length} 个车型`);
                        return availableCarTypes;
                    }
                }

                // API调用失败，使用备用车型列表
                console.warn('⚠️ 无法从API获取车型列表，使用备用配置');
                availableCarTypes = getBackupCarTypes();
                updateCarTypeSelectors();
                return availableCarTypes;

            } catch (error) {
                console.error('获取车型列表时出错:', error);
                // 使用备用车型列表
                availableCarTypes = getBackupCarTypes();
                updateCarTypeSelectors();
                return availableCarTypes;
            }
        }

        /**
         * @function getBackupCarTypes - 获取备用车型列表
         * @returns {Array} 备用车型配置
         * @description 当API不可用时使用的备用车型列表
         */
        function getBackupCarTypes() {
            return [
                { id: 1, type: 'Comfort 5 Seater', seat_number: 5, priority: 1 },
                { id: 5, type: 'Economy 5 Seater', seat_number: 5, priority: 2 },
                { id: 15, type: 'Economy 7 Seater', seat_number: 7, priority: 3 },
                { id: 20, type: 'Comfort 7 Seater', seat_number: 7, priority: 4 },
                { id: 25, type: 'Mini Bus 14 Seater', seat_number: 14, priority: 5 },
                { id: 26, type: 'Bus 20+ Seater', seat_number: 20, priority: 6 },
                { id: 32, type: 'Luxury 5 Seater', seat_number: 5, priority: 7 },
                { id: 33, type: 'Luxury 7 Seater', seat_number: 7, priority: 8 },
                { id: 36, type: 'Premium Luxury', seat_number: 5, priority: 9 },
                { id: 38, type: 'Economy Plus', seat_number: 5, priority: 10 }
            ];
        }

        /**
         * @function updateCarTypeSelectors - 更新所有车型选择器
         * @description 使用最新的车型数据更新手动输入表单的车型选择器
         */
        function updateCarTypeSelectors() {
            const manualSelector = document.getElementById('manualCarType');
            if (!manualSelector) return;

            // 保存当前选中的值
            const currentValue = manualSelector.value;

            // 清空并重新填充选项
            manualSelector.innerHTML = '';

            if (availableCarTypes.length === 0) {
                manualSelector.innerHTML = '<option value="">暂无可用车型</option>';
                return;
            }

            // 按优先级或座位数排序
            const sortedCarTypes = [...availableCarTypes].sort((a, b) => {
                return (a.priority || a.seat_number || 0) - (b.priority || b.seat_number || 0);
            });

            sortedCarTypes.forEach(carType => {
                const option = document.createElement('option');
                option.value = carType.id;
                option.textContent = `${carType.type || carType.name || 'Unknown'} (${carType.seat_number || 'N/A'} 座)`;
                manualSelector.appendChild(option);
            });

            // 恢复之前选中的值，如果仍然有效
            if (currentValue && availableCarTypes.find(ct => ct.id == currentValue)) {
                manualSelector.value = currentValue;
            } else {
                // 选择第一个可用选项
                manualSelector.value = sortedCarTypes[0]?.id || '';
            }

            console.log(`🚗 车型选择器已更新，共 ${availableCarTypes.length} 个选项`);
        }

        /**
         * @function validateCarType - 验证车型ID是否有效
         * @param {number} carTypeId - 车型ID
         * @returns {Object} 验证结果 {isValid: boolean, carType: Object|null, error: string}
         */
        function validateCarType(carTypeId) {
            if (!carTypeId) {
                return { isValid: false, carType: null, error: '请选择车型' };
            }

            const carType = availableCarTypes.find(ct => ct.id == carTypeId);
            if (!carType) {
                return {
                    isValid: false,
                    carType: null,
                    error: `车型ID ${carTypeId} 不存在，请选择有效的车型`
                };
            }

            return { isValid: true, carType: carType, error: null };
        }

        /**
         * @function getCarTypeDisplayName - 获取车型显示名称
         * @param {number} carTypeId - 车型ID
         * @returns {string} 车型显示名称
         */
        function getCarTypeDisplayName(carTypeId) {
            const carType = availableCarTypes.find(ct => ct.id == carTypeId);
            if (carType) {
                return `${carType.type || carType.name || 'Unknown'} (ID: ${carTypeId})`;
            }
            return `车型ID: ${carTypeId} (未知车型)`;
        }

        /**
         * @function getRegionDisplayName - 获取地区显示名称
         * @param {number} regionId - 地区ID
         * @returns {string} 地区显示名称
         */
        function getRegionDisplayName(regionId) {
            const region = availableRegions.find(r => r.id == regionId);
            if (region) {
                return `${region.name} (ID: ${regionId})`;
            }
            return `地区ID: ${regionId} (未知地区)`;
        }

        /**
         * @function getLanguagesDisplayName - 获取语言显示名称
         * @param {Array} languageIds - 语言ID数组
         * @returns {string} 语言显示名称
         */
        function getLanguagesDisplayName(languageIds) {
            if (!Array.isArray(languageIds) || languageIds.length === 0) {
                return '未指定语言';
            }

            const languageNames = languageIds.map(id => {
                const language = availableLanguages.find(lang => lang.id == id);
                return language ? language.name : `语言ID: ${id}`;
            });

            return languageNames.join(', ');
        }

        /**
         * @function getSubCategoryDisplayName - 获取子分类显示名称
         * @param {number} subCategoryId - 子分类ID
         * @returns {string} 子分类显示名称
         */
        function getSubCategoryDisplayName(subCategoryId) {
            const subCategory = availableSubCategories.find(sc => sc.id == subCategoryId);
            if (subCategory) {
                return `${subCategory.name} (ID: ${subCategoryId})`;
            }
            return `子分类ID: ${subCategoryId} (未知类型)`;
        }

        // ========== 子分类管理功能 ==========

        /**
         * @function loadSubCategories - 从API加载可用子分类列表
         * @returns {Array} 子分类列表
         * @description 获取GoMyHire API支持的所有订单子分类
         */
        async function loadSubCategories() {
            if (!authToken) return [];

            try {
                const response = await fetch(`${API_BASE_URL}/sub_categories`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && Array.isArray(data.data)) {
                        availableSubCategories = data.data;
                        console.log(`✅ 成功加载 ${availableSubCategories.length} 个子分类`);
                        return availableSubCategories;
                    } else if (data && Array.isArray(data)) {
                        availableSubCategories = data;
                        console.log(`✅ 成功加载 ${availableSubCategories.length} 个子分类`);
                        return availableSubCategories;
                    }
                }

                // API调用失败，使用备用子分类列表
                console.warn('⚠️ 无法从API获取子分类列表，使用备用配置');
                availableSubCategories = getBackupSubCategories();
                return availableSubCategories;

            } catch (error) {
                console.error('获取子分类列表时出错:', error);
                availableSubCategories = getBackupSubCategories();
                return availableSubCategories;
            }
        }

        /**
         * @function getBackupSubCategories - 获取备用子分类列表
         * @returns {Array} 备用子分类配置
         */
        function getBackupSubCategories() {
            return [
                { id: 2, main_category: 'Airport Transfer', name: 'Pickup Service', type: 'pickup' },
                { id: 3, main_category: 'Airport Transfer', name: 'Drop-off Service', type: 'dropoff' },
                { id: 4, main_category: 'Charter Service', name: 'Charter Service', type: 'charter' }
            ];
        }

        /**
         * @function validateSubCategory - 验证子分类ID是否有效
         * @param {number} subCategoryId - 子分类ID
         * @returns {Object} 验证结果
         */
        function validateSubCategory(subCategoryId) {
            if (!subCategoryId) {
                return { isValid: false, subCategory: null, error: '请选择订单类型' };
            }

            const subCategory = availableSubCategories.find(sc => sc.id == subCategoryId);
            if (!subCategory) {
                return {
                    isValid: false,
                    subCategory: null,
                    error: `子分类ID ${subCategoryId} 不存在，请选择有效的订单类型`
                };
            }

            return { isValid: true, subCategory: subCategory, error: null };
        }

        /**
         * @function getSubCategoryByType - 根据订单类型获取子分类ID
         * @param {string} orderType - 订单类型 (pickup/dropoff/charter)
         * @returns {number|null} 子分类ID
         */
        function getSubCategoryByType(orderType) {
            const subCategory = availableSubCategories.find(sc =>
                sc.type === orderType ||
                (orderType === 'pickup' && sc.name?.toLowerCase().includes('pickup')) ||
                (orderType === 'dropoff' && sc.name?.toLowerCase().includes('drop')) ||
                (orderType === 'charter' && sc.name?.toLowerCase().includes('charter'))
            );
            return subCategory ? subCategory.id : null;
        }

        // ========== 地区管理功能 ==========

        /**
         * @function loadRegions - 从API加载可用地区列表
         * @returns {Array} 地区列表
         */
        async function loadRegions() {
            if (!authToken) return [];

            try {
                // 尝试多个可能的端点
                const endpoints = ['/regions', '/driving_regions', '/areas'];

                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'Authorization': `Bearer ${authToken}`
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.data && Array.isArray(data.data)) {
                                availableRegions = data.data;
                                updateRegionSelector();
                                console.log(`✅ 成功从 ${endpoint} 加载 ${availableRegions.length} 个地区`);
                                return availableRegions;
                            } else if (data && Array.isArray(data)) {
                                availableRegions = data;
                                updateRegionSelector();
                                console.log(`✅ 成功从 ${endpoint} 加载 ${availableRegions.length} 个地区`);
                                return availableRegions;
                            }
                        }
                    } catch (endpointError) {
                        console.log(`尝试端点 ${endpoint} 失败:`, endpointError.message);
                        continue;
                    }
                }

                // 所有端点都失败，使用备用配置
                console.warn('⚠️ 无法从API获取地区列表，使用备用配置');
                availableRegions = getBackupRegions();
                updateRegionSelector();
                return availableRegions;

            } catch (error) {
                console.error('获取地区列表时出错:', error);
                availableRegions = getBackupRegions();
                updateRegionSelector();
                return availableRegions;
            }
        }

        /**
         * @function getBackupRegions - 获取备用地区列表
         * @returns {Array} 备用地区配置
         */
        function getBackupRegions() {
            return [
                { id: 1, name: '吉隆坡/雪兰莪', code: 'KL', active: true },
                { id: 2, name: '槟城', code: 'PG', active: true },
                { id: 3, name: '柔佛', code: 'JH', active: true },
                { id: 4, name: '沙巴', code: 'SB', active: true },
                { id: 12, name: '马六甲', code: 'ML', active: true }
            ];
        }

        /**
         * @function updateRegionSelector - 更新地区选择器
         */
        function updateRegionSelector() {
            const regionSelector = document.getElementById('manualRegion');
            if (!regionSelector) return;

            const currentValue = regionSelector.value;
            regionSelector.innerHTML = '';

            if (availableRegions.length === 0) {
                regionSelector.innerHTML = '<option value="">暂无可用地区</option>';
                return;
            }

            availableRegions.forEach(region => {
                const option = document.createElement('option');
                option.value = region.id;
                option.textContent = region.name || `地区 ${region.id}`;
                regionSelector.appendChild(option);
            });

            // 恢复之前选中的值
            if (currentValue && availableRegions.find(r => r.id == currentValue)) {
                regionSelector.value = currentValue;
            } else {
                regionSelector.value = availableRegions[0]?.id || '';
            }

            console.log(`🌍 地区选择器已更新，共 ${availableRegions.length} 个选项`);
        }

        /**
         * @function validateRegion - 验证地区ID是否有效
         * @param {number} regionId - 地区ID
         * @returns {Object} 验证结果
         */
        function validateRegion(regionId) {
            if (!regionId) {
                return { isValid: false, region: null, error: '请选择服务地区' };
            }

            const region = availableRegions.find(r => r.id == regionId);
            if (!region) {
                return {
                    isValid: false,
                    region: null,
                    error: `地区ID ${regionId} 不存在，请选择有效的服务地区`
                };
            }

            return { isValid: true, region: region, error: null };
        }

        // ========== 语言管理功能 ==========

        /**
         * @function loadLanguages - 从API加载可用语言列表
         * @returns {Array} 语言列表
         */
        async function loadLanguages() {
            if (!authToken) return [];

            try {
                const response = await fetch(`${API_BASE_URL}/languages`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && Array.isArray(data.data)) {
                        availableLanguages = data.data;
                        console.log(`✅ 成功加载 ${availableLanguages.length} 种语言`);
                        return availableLanguages;
                    } else if (data && Array.isArray(data)) {
                        availableLanguages = data;
                        console.log(`✅ 成功加载 ${availableLanguages.length} 种语言`);
                        return availableLanguages;
                    }
                }

                // API调用失败，使用备用语言列表
                console.warn('⚠️ 无法从API获取语言列表，使用备用配置');
                availableLanguages = getBackupLanguages();
                return availableLanguages;

            } catch (error) {
                console.error('获取语言列表时出错:', error);
                availableLanguages = getBackupLanguages();
                return availableLanguages;
            }
        }

        /**
         * @function getBackupLanguages - 获取备用语言列表
         * @returns {Array} 备用语言配置
         */
        function getBackupLanguages() {
            return [
                { id: 2, name: 'English', code: 'en', active: true },
                { id: 3, name: 'Bahasa Malaysia', code: 'ms', active: true },
                { id: 4, name: '中文', code: 'zh', active: true }
            ];
        }

        /**
         * @function getLanguagesByCustomerType - 根据客户类型获取语言ID数组
         * @param {string} customerType - 客户类型 (chinese/english/malay)
         * @returns {Array} 语言ID数组
         */
        function getLanguagesByCustomerType(customerType) {
            if (availableLanguages.length === 0) {
                // 使用备用配置
                switch (customerType) {
                    case 'chinese': return [2, 4]; // 英文 + 中文
                    case 'english': return [2]; // 英文
                    case 'malay': return [3]; // 马来文
                    default: return [2]; // 默认英文
                }
            }

            // 使用动态语言数据
            const languageMap = {
                'chinese': ['en', 'zh'],
                'english': ['en'],
                'malay': ['ms']
            };

            const targetCodes = languageMap[customerType] || ['en'];
            const languageIds = [];

            targetCodes.forEach(code => {
                const language = availableLanguages.find(lang =>
                    lang.code === code ||
                    lang.name?.toLowerCase().includes(code) ||
                    (code === 'zh' && lang.name?.includes('中文'))
                );
                if (language) {
                    languageIds.push(language.id);
                }
            });

            return languageIds.length > 0 ? languageIds : [2]; // 默认返回英文
        }

        /**
         * @function validateLanguages - 验证语言ID数组是否有效
         * @param {Array} languageIds - 语言ID数组
         * @returns {Object} 验证结果
         */
        function validateLanguages(languageIds) {
            if (!Array.isArray(languageIds) || languageIds.length === 0) {
                return { isValid: false, languages: [], error: '请至少选择一种语言' };
            }

            const validLanguages = [];
            const invalidIds = [];

            languageIds.forEach(id => {
                const language = availableLanguages.find(lang => lang.id == id);
                if (language) {
                    validLanguages.push(language);
                } else {
                    invalidIds.push(id);
                }
            });

            if (invalidIds.length > 0) {
                return {
                    isValid: false,
                    languages: validLanguages,
                    error: `语言ID ${invalidIds.join(', ')} 不存在`
                };
            }

            return { isValid: true, languages: validLanguages, error: null };
        }

        // 加载后台用户列表
        async function loadBackendUsers() {
            if (!authToken) return [];

            try {
                const response = await fetch(`${API_BASE_URL}/backend_users?search=`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && Array.isArray(data.data)) {
                        availableBackendUsers = data.data;
                        updateBackendUserSelector();
                        // 使用智能选择替代原来的默认选择
                        smartSelectBackendUser();
                        return availableBackendUsers;
                    }
                }
                return [];
            } catch (error) {
                console.error('获取后台用户时出错:', error);
                return [];
            }
        }

        // 更新后台用户选择器
        function updateBackendUserSelector() {
            const selector = document.getElementById('backendUserSelect');
            if (!selector) return;

            selector.innerHTML = '<option value="">选择后台用户...</option>';
            availableBackendUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (ID: ${user.id}) - ${user.role || '未知角色'}`;
                selector.appendChild(option);
            });

            const selectorContainer = document.getElementById('backendUserSelector');
            if (selectorContainer) selectorContainer.style.display = 'block';

            updateBackendUserInfo(`找到 ${availableBackendUsers.length} 个可用后台用户`);
        }

        // 选择后台用户
        function selectBackendUser(userId = null) {
            if (!userId) {
                const selector = document.getElementById('backendUserSelect');
                if (!selector || !selector.value) return;
                userId = parseInt(selector.value);
            }

            const user = availableBackendUsers.find(u => u.id === userId);
            if (!user) return;

            selectedBackendUser = user;
            const selector = document.getElementById('backendUserSelect');
            if (selector) selector.value = userId;

            updateSelectedBackendUserInfo(user);
        }

        // 更新状态显示
        function updateAuthStatus(isAuthenticated, customMessage = null) {
            const indicator = document.getElementById('authStatus');
            if (indicator) {
                if (isAuthenticated) {
                    indicator.className = 'auth-status auth-success';
                    indicator.textContent = customMessage || '✅ 认证已完成';
                } else {
                    indicator.className = 'auth-status auth-pending';
                    indicator.textContent = customMessage || '正在认证中...';
                }
            }
        }

        function updateCurrentAccountInfo(message) {
            const infoElement = document.getElementById('currentAccountInfo');
            if (infoElement) infoElement.textContent = message;
        }

        function updateBackendUserInfo(message) {
            const infoElement = document.getElementById('backendUserInfo');
            if (infoElement) infoElement.textContent = message;
        }

        function updateSelectedBackendUserInfo(user) {
            const infoElement = document.getElementById('selectedBackendUserInfo');
            if (infoElement && user) {
                infoElement.innerHTML = `
                    <strong>当前选中:</strong> ${user.name}<br>
                    <strong>用户ID:</strong> ${user.id}<br>
                    <strong>角色:</strong> ${user.role || '未指定'}
                `;
            } else if (infoElement) {
                infoElement.textContent = '未选择后台用户';
            }
        }

        // 初始化账号选择器
        function initializeAccountSelector() {
            const selector = document.getElementById('accountSelector');
            if (!selector) return;

            selector.innerHTML = '<option value="">选择邮箱账号...</option>';
            realLoginAccounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = account.email;
                if (account.isDefault) option.selected = true;
                selector.appendChild(option);
            });
        }

        // 测试所有邮箱账号
        async function testAllAccounts() {
            console.log('开始测试所有邮箱账号...');
            for (const account of realLoginAccounts) {
                console.log(`测试邮箱: ${account.email}`);
                const success = await authenticateAccount(account);
                if (success) {
                    currentAccount = account;
                    // 并行加载所有必要数据
                    await Promise.all([
                        loadBackendUsers(),
                        loadCarTypes(),
                        loadSubCategories(),
                        loadRegions(),
                        loadLanguages()
                    ]);
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            console.log('所有邮箱账号测试完成');
        }

        // ========== 手动输入订单测试功能 ==========

        /**
         * @function updateManualSubCategory - 根据订单类型更新子分类
         * @description 当用户选择订单类型时，自动设置对应的子分类ID
         */
        function updateManualSubCategory() {
            // 这个函数可以在未来扩展，目前订单类型和子分类的映射在提交时处理
            console.log('订单类型已更新');
        }

        /**
         * @function initializeManualForm - 初始化手动输入表单
         * @description 设置默认值和当前日期
         */
        function initializeManualForm() {
            // 设置默认日期为明天
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateString = tomorrow.toISOString().split('T')[0];
            document.getElementById('manualDate').value = dateString;

            // 生成默认OTA参考号
            const timestamp = Date.now();
            document.getElementById('manualReference').value = `MANUAL_TEST_${timestamp}`;
        }

        /**
         * @function collectManualOrderData - 收集手动输入的订单数据
         * @returns {Object} 订单数据对象
         * @description 从表单收集所有输入数据并格式化为API所需格式
         */
        function collectManualOrderData() {
            const orderType = document.getElementById('manualOrderType').value;
            const carTypeId = parseInt(document.getElementById('manualCarType').value);
            const regionId = parseInt(document.getElementById('manualRegion').value);

            // 使用动态子分类数据
            let subCategoryId = getSubCategoryByType(orderType);
            if (!subCategoryId) {
                // 回退到硬编码映射
                const subCategoryMap = {
                    'pickup': 2,    // 接机
                    'dropoff': 3,   // 送机
                    'charter': 4    // 包车
                };
                subCategoryId = subCategoryMap[orderType];
            }

            // 使用智能语言选择（默认英文，可以根据客户信息智能选择）
            let languageIds = [2]; // 默认英文
            if (availableLanguages.length > 0) {
                const englishLang = availableLanguages.find(lang =>
                    lang.code === 'en' || lang.name?.toLowerCase().includes('english')
                );
                if (englishLang) {
                    languageIds = [englishLang.id];
                }
            }

            const orderData = {
                sub_category_id: subCategoryId,
                car_type_id: carTypeId,
                incharge_by_backend_user_id: selectedBackendUser?.id || 1, // 将在prepareOrderData中智能匹配
                ota_reference_number: document.getElementById('manualReference').value || `MANUAL_${Date.now()}`,
                customer_name: document.getElementById('manualCustomerName').value,
                customer_contact: document.getElementById('manualCustomerContact').value,
                customer_email: document.getElementById('manualCustomerEmail').value,
                pickup: document.getElementById('manualPickup').value,
                destination: document.getElementById('manualDestination').value,
                date: document.getElementById('manualDate').value,
                time: document.getElementById('manualTime').value,
                passenger_number: parseInt(document.getElementById('manualPassengers').value),
                luggage_number: parseInt(document.getElementById('manualLuggage').value),
                driving_region_id: regionId,
                languages_id_array: languageIds,
                extra_requirement: document.getElementById('manualRequirement').value
            };

            return orderData;
        }

        /**
         * @function validateManualOrderData - 验证手动输入的订单数据
         * @param {Object} orderData - 订单数据
         * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
         */
        function validateManualOrderData(orderData) {
            const errors = [];

            // 必填字段验证
            if (!orderData.customer_name?.trim()) errors.push('客户姓名不能为空');
            if (!orderData.customer_contact?.trim()) errors.push('客户电话不能为空');
            if (!orderData.customer_email?.trim()) errors.push('客户邮箱不能为空');
            if (!orderData.pickup?.trim()) errors.push('接机地址不能为空');
            if (!orderData.destination?.trim()) errors.push('目的地址不能为空');
            if (!orderData.date) errors.push('服务日期不能为空');
            if (!orderData.time) errors.push('服务时间不能为空');

            // 子分类验证
            const subCategoryValidation = validateSubCategory(orderData.sub_category_id);
            if (!subCategoryValidation.isValid) {
                errors.push(subCategoryValidation.error);
            }

            // 车型验证
            const carTypeValidation = validateCarType(orderData.car_type_id);
            if (!carTypeValidation.isValid) {
                errors.push(carTypeValidation.error);
            }

            // 地区验证
            const regionValidation = validateRegion(orderData.driving_region_id);
            if (!regionValidation.isValid) {
                errors.push(regionValidation.error);
            }

            // 语言验证
            const languageValidation = validateLanguages(orderData.languages_id_array);
            if (!languageValidation.isValid) {
                errors.push(languageValidation.error);
            }

            // 数值验证
            if (orderData.passenger_number < 1) errors.push('乘客人数必须大于0');
            if (orderData.luggage_number < 0) errors.push('行李件数不能为负数');

            // 邮箱格式验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (orderData.customer_email && !emailRegex.test(orderData.customer_email)) {
                errors.push('邮箱格式不正确');
            }

            // 日期验证（不能是过去的日期）
            const selectedDate = new Date(orderData.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (selectedDate < today) {
                errors.push('服务日期不能是过去的日期');
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        /**
         * @function previewManualOrder - 预览手动输入的订单数据
         * @description 显示格式化的订单数据供用户确认
         */
        function previewManualOrder() {
            try {
                const orderData = collectManualOrderData();
                const validation = validateManualOrderData(orderData);

                if (!validation.isValid) {
                    alert('数据验证失败：\n' + validation.errors.join('\n'));
                    return;
                }

                // 使用智能匹配处理订单数据
                const processedData = prepareOrderData(orderData);

                // 格式化显示
                const preview = `
📋 订单数据预览：

🏷️ 基本信息：
• 订单类型: ${orderData.sub_category_id === 2 ? '接机服务' : orderData.sub_category_id === 3 ? '送机服务' : '包车服务'}
• OTA参考号: ${processedData.ota_reference_number}
• 负责人ID: ${processedData.incharge_by_backend_user_id} ${getSmartBackendUserId() ? '(智能匹配)' : '(默认选择)'}

👤 客户信息：
• 姓名: ${processedData.customer_name}
• 电话: ${processedData.customer_contact}
• 邮箱: ${processedData.customer_email}

🚗 服务信息：
• 接机地址: ${processedData.pickup}
• 目的地址: ${processedData.destination}
• 服务日期: ${processedData.date}
• 服务时间: ${processedData.time}
• 乘客人数: ${processedData.passenger_number}人
• 行李件数: ${processedData.luggage_number}件
• 车型: ${getCarTypeDisplayName(processedData.car_type_id)}
• 服务地区: ${getRegionDisplayName(processedData.driving_region_id)}
• 语言要求: ${getLanguagesDisplayName(processedData.languages_id_array)}

📝 特殊要求：
${processedData.extra_requirement || '无'}

确认数据无误后，点击"提交测试订单"按钮进行测试。
                `;

                alert(preview);

            } catch (error) {
                console.error('预览订单数据时出错:', error);
                alert('预览失败: ' + error.message);
            }
        }

        /**
         * @function submitManualOrder - 提交手动输入的测试订单
         * @description 验证数据并提交到GoMyHire API
         */
        async function submitManualOrder() {
            const resultContainer = document.getElementById('manualOrderResult');

            try {
                // 验证认证状态
                if (!authToken) {
                    throw new Error('请先选择邮箱账号进行认证');
                }

                // 收集和验证数据
                const orderData = collectManualOrderData();
                const validation = validateManualOrderData(orderData);

                if (!validation.isValid) {
                    throw new Error('数据验证失败：\n' + validation.errors.join('\n'));
                }

                // 显示提交状态
                resultContainer.style.display = 'block';
                resultContainer.className = 'test-result result-pending';
                resultContainer.innerHTML = '🔄 正在提交手动测试订单...';

                // 准备订单数据（应用智能匹配）
                const processedOrderData = prepareOrderData(orderData);
                const startTime = Date.now();

                // 发送API请求
                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(processedOrderData)
                });

                const responseTime = Date.now() - startTime;
                const responseText = await response.text();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`无效的JSON响应: ${parseError.message}`);
                }

                if (result.status === true || result.status === 'true') {
                    // 更新统计
                    orderTestStats.success++;
                    orderTestStats.total++;
                    updateStats();

                    resultContainer.className = 'test-result result-success';
                    resultContainer.innerHTML = `
                        ✅ <strong>手动订单提交成功</strong><br>
                        订单ID: ${result.order_id || result.data?.order_id || 'N/A'}<br>
                        消息: ${result.message || '订单创建成功'}<br>
                        负责人: ${processedOrderData.incharge_by_backend_user_id} ${getSmartBackendUserId() ? '(智能匹配)' : '(默认选择)'}<br>
                        <small>响应时间: ${responseTime}ms</small>
                    `;
                } else {
                    throw new Error(result.message || result.error || '订单创建失败');
                }

            } catch (error) {
                // 更新统计
                orderTestStats.failed++;
                orderTestStats.total++;
                updateStats();

                resultContainer.style.display = 'block';
                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>手动订单提交失败</strong><br>
                    错误: ${error.message}<br>
                    <small>请检查输入数据并重试</small>
                `;

                console.error('手动订单提交失败:', error);
            }
        }

        /**
         * @function resetManualForm - 重置手动输入表单
         * @description 清空所有输入字段并恢复默认值
         */
        function resetManualForm() {
            // 重置选择器
            document.getElementById('manualOrderType').value = 'pickup';

            // 重置车型选择器为第一个可用选项
            const carTypeSelector = document.getElementById('manualCarType');
            if (carTypeSelector && carTypeSelector.options.length > 0) {
                carTypeSelector.value = carTypeSelector.options[0].value;
            }

            document.getElementById('manualRegion').value = '1';

            // 重置客户信息
            document.getElementById('manualCustomerName').value = '测试客户';
            document.getElementById('manualCustomerContact').value = '+***********';
            document.getElementById('manualCustomerEmail').value = '<EMAIL>';

            // 重置地址信息
            document.getElementById('manualPickup').value = 'Kuala Lumpur International Airport (KLIA)';
            document.getElementById('manualDestination').value = 'KLCC - Kuala Lumpur City Centre';

            // 重置时间信息
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('manualDate').value = tomorrow.toISOString().split('T')[0];
            document.getElementById('manualTime').value = '10:00';

            // 重置数量信息
            document.getElementById('manualPassengers').value = '2';
            document.getElementById('manualLuggage').value = '2';

            // 重置其他字段
            document.getElementById('manualReference').value = `MANUAL_TEST_${Date.now()}`;
            document.getElementById('manualRequirement').value = 'TESTING - 手动输入测试订单，请勿处理';

            // 隐藏结果
            const resultContainer = document.getElementById('manualOrderResult');
            if (resultContainer) {
                resultContainer.style.display = 'none';
            }

            console.log('手动输入表单已重置');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('GoMyHire API测试工具初始化 - 精简版');
            try {
                initializeAccountSelector();
                initializeManualForm(); // 初始化手动输入表单
                updateStats();

                updateAuthStatus(false, '正在初始化...');
                updateCurrentAccountInfo('正在加载默认邮箱账号...');

                const defaultAccount = realLoginAccounts.find(acc => acc.isDefault);
                if (defaultAccount) {
                    await switchToAccount(defaultAccount.id);
                } else {
                    updateAuthStatus(false, '请选择邮箱账号');
                    updateCurrentAccountInfo('请从上方选择邮箱账号');
                    // 即使没有认证，也加载备用数据
                    availableCarTypes = getBackupCarTypes();
                    availableSubCategories = getBackupSubCategories();
                    availableRegions = getBackupRegions();
                    availableLanguages = getBackupLanguages();
                    updateCarTypeSelectors();
                    updateRegionSelector();
                }

                console.log('测试工具初始化完成');
                console.log(`🚗 当前可用车型数量: ${availableCarTypes.length}`);
                console.log(`👤 当前可用后台用户数量: ${availableBackendUsers.length}`);
                console.log(`📋 当前可用子分类数量: ${availableSubCategories.length}`);
                console.log(`🌍 当前可用地区数量: ${availableRegions.length}`);
                console.log(`🗣️ 当前可用语言数量: ${availableLanguages.length}`);
            } catch (error) {
                console.error('页面初始化失败:', error);
                updateAuthStatus(false, '❌ 初始化失败');
                updateCurrentAccountInfo('初始化失败');
            }
        });
    </script>
</body>
</html>
